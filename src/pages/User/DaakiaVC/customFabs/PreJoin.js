/* eslint-disable */
import React, { useState, useCallback, useEffect, useMemo } from "react";
import { Row, Col, Input, Button } from "antd";
import { EyeTwoTone, EyeInvisibleOutlined } from "@ant-design/icons";
import { datadogLogs } from "@datadog/browser-logs";

import axios from "axios";

import { PreJoinAudioVideo } from "./PreJoinAudioVideo";
import { constants } from "../utils/constants";
import { ReactComponent as DaakiaLogo } from "./icons/DaakiaProductLogo.svg";

import { getLocalStorage } from "../utils/helper";
import { PrejoinService } from "../services/PrejoinServices";

import "../styles/Prejoin.scss";
import "../styles/index.scss";
import "../styles/videoHeightControl.scss";
import { Loader } from "../components/Loader";
import { Link } from "react-router-dom";
import isElectron from "is-electron";
import TitleBar from "../components/titleBar";
import { Toast } from "react-bootstrap";
import { VirtualBackgroundDrawer } from "../components/settings/VirtualBackgroundDrawer";
import StatusNotification from "../components/StatusNotification/StatusNotification";
import { useSaasHelpers } from "../SaaS/helpers/helpers";
// import { VideoConferenceService } from "../../../../services"; // Need to bring inside

export function Prejoin({
  setServerDetails,
  id,
  setPreJoinShow,
  isHost,
  isPasswordProtected,
  meetingDetails,
  setClientPreferedServerId,
  userChoices,
  setUserChoices,
  isLobbyMode,
  isWebinarMode,
  setIsPipWindow,
  isPipWindow,
  room,
  backgrounds,
  setBackgrounds,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  deviceIdAudio,
  setDeviceIdAudio,
  deviceIdVideo,
  setDeviceIdVideo,
  brightness,
  onBrightnessChange,
  // Volume props (following brightness pattern, no RPC needed)
  outputVolume,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff,
  onAutoAudioOffChange,
  speakerDeviceId,
  setSpeakerDeviceId,
}) {
  // Get SaaS configuration from context
  const { saasMeetingConfigurations, saasHostToken, isSaas } = useSaasHelpers();
  const [username, setUsername] = useState(
    isHost ? meetingDetails.host : userChoices.username || ""
  );
  const [password, setPassword] = useState("");
  const [isValid, setIsValid] = useState(false);
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [canJoin, setCanJoin] = useState(true);
  const [isRejected, setIsRejected] = useState(false);
  const [isMeetingStarted, setIsMeetingStarted] = useState(false);
  const [cohostDetail, setCohostDetail] = useState(null);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastStatus, setToastStatus] = useState("");
  const [queryParams, setQueryParams] = useState({});

  // Determine if we should show the side-by-side layout
  const showSideBySideLayout = useMemo(() => {
    // Show side-by-side when there's only the name field (no password or email fields)
    return (
      (!isPasswordProtected && !meetingDetails?.is_common_password) ||
      isHost ||
      cohostDetail?.current_session_uid === meetingDetails?.current_session_uid
    );
  }, [
    isPasswordProtected,
    meetingDetails?.is_common_password,
    isHost,
    cohostDetail,
    meetingDetails,
  ]);

  useEffect(() => {
    // Parse the URL search parameters
    const params = new URLSearchParams(window.location.search);
    let name = params.get("name") || ""; // Default to an empty string if not present
    // name is urlencoded, so decoded it
    name = decodeURIComponent(name);
    if (name) {
      setQueryParams((prev) => ({ ...prev, name }));
    }
    const edit = params.get("edit");
    if (edit) {
      setQueryParams((prev) => ({ ...prev, edit: edit === "true" }));
    }
    const custom_metadata = params.get("custom_metadata");
    if (custom_metadata) {
      // decode base 64
      const customMetadata = JSON.parse(atob(custom_metadata));
      setQueryParams((prev) => ({ ...prev, custom_metadata: customMetadata }));
    }
  }, []);

  setTimeout(() => {
    window.history.replaceState(null, "", window.location.pathname);
  }, 1000);

  const getRegion = useCallback(async () => {
    const maxAttempts = 3;
    let attempts = 0;

    while (attempts < maxAttempts) {
      try {
        const response = await axios.get(constants.REGION_LOCATOR);
        setClientPreferedServerId(
          () => response?.data?.preferred_video_server_id
        );
        return response;
      } catch (error) {
        setToastMessage(error.message);
        setToastStatus("error");
        setShowToast(true);
        datadogLogs.logger.error(
          `Error getting region (attempt ${attempts + 1})`,
          {
            error,
          }
        );
        attempts += 1;
        if (attempts >= maxAttempts) {
          datadogLogs.logger.error(
            `Error getting region (attempt ${attempts + 1})`,
            {
              error,
              message: "Setting up default server to ap1",
            }
          );
          return { data: { preferred_video_server_id: "ap1" } };
        }
      }
    }
  }, []);

  useEffect(() => {
    try {
      const detail = getLocalStorage(constants.CO_HOST_TOKEN);
      if (detail?.current_session_uid !== meetingDetails?.current_session_uid) {
        localStorage.removeItem(constants.CO_HOST_TOKEN);
        setCohostDetail(null);
      } else {
        setCohostDetail(detail);
      }
    } catch (error) {
      datadogLogs.logger.error(
        `Error getting cohost detail from local storage`,
        {
          error,
        }
      );
    }
  }, [meetingDetails?.current_session_uid]);

  const getServerDetails = useCallback(
    async (user) => {
      if (!isValid) return;
      setIsLoading(true);
      try {
        if (
          isPasswordProtected &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          const validatePassword = await PrejoinService.verifyPassword(
            email,
            password,
            id
          );
          if (validatePassword.success === 0) {
            setToastMessage("Email or Password Incorrect");
            setToastStatus("error");
            setShowToast(true);
            setIsLoading(false);
            return;
          }
        }
        if (
          meetingDetails?.is_common_password &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          const validatePassword = await PrejoinService.commonPasswordVerify(
            id,
            password
          );
          if (validatePassword.success === 0) {
            setToastMessage("Email or Password Incorrect");
            setToastStatus("error");
            setShowToast(true);
            setIsLoading(false);
            return;
          }
        }
        const region = await getRegion();
        let joinMeetingPayload = {
          preferred_video_server_id:
            region && region.data && region.data.preferred_video_server_id
              ? region.data.preferred_video_server_id
              : "ap1",
          meeting_uid: id,
          display_name: user.username,
          custom_metadata: isSaas ? queryParams.custom_metadata : undefined,
        };
        if (
          isLobbyMode &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          const lobbyResponse = await PrejoinService.addParticipantToLobby(
            id,
            user.username
          );

          if (lobbyResponse.success === 0) {
            // notify("error", lobbyResponse.message);
            setToastMessage(lobbyResponse.message);
            setToastStatus("error");
            setShowToast(true);
            setIsLoading(false);
            return;
          }
          joinMeetingPayload = {
            ...joinMeetingPayload,
            lobby_request_id: lobbyResponse.data.request_id,
          };
        }
        if (
          cohostDetail?.current_session_uid ===
          meetingDetails?.current_session_uid
        ) {
          joinMeetingPayload = {
            ...joinMeetingPayload,
            meeting_attendance_uid: String(cohostDetail?.meeting_attendance_id),
          };
        }
        const response = await PrejoinService.joinMeeting(
          joinMeetingPayload,
          {
            username,
            isHost,
          },
          saasHostToken
        );

        if (response?.success === 0) {
          // notify("error", response.message);
          setToastMessage(response.message);
          setToastStatus("error");
          setShowToast(true);
          setIsLoading(false);
          return;
        }

        if (
          !response.data.participant_can_join &&
          !isHost &&
          cohostDetail?.current_session_uid !==
            meetingDetails?.current_session_uid
        ) {
          // if (!isLobbyMode) {
          //   notify("error", "Meeting has not started yet");
          // }
          if (
            (isLobbyMode || isWebinarMode) &&
            response.data?.meeting_started
          ) {
            setIsMeetingStarted(true);
          }
          setCanJoin(false);
          const intervalId = setInterval(async () => {
            try {
              const pollingResponse = await PrejoinService.joinMeeting(
                joinMeetingPayload,
                {
                  username,
                  isHost,
                }
              );

              if (
                (isLobbyMode || isWebinarMode) &&
                pollingResponse.data?.meeting_started
              ) {
                setIsMeetingStarted(true);
              }

              if (isLobbyMode && pollingResponse.data?.is_rejected) {
                clearInterval(intervalId);
                // notify("error", pollingResponse.message);
                setCanJoin(false);
                setIsRejected(true);
              }
              if (pollingResponse.data.participant_can_join) {
                clearInterval(intervalId);
                setServerDetails(() => ({
                  serverUrl: pollingResponse.data.livekit_server_URL,
                  token: pollingResponse.data.access_token,
                }));
                setPreJoinShow(() => false);
                setCanJoin(true);
              }
            } catch (error) {
              setToastMessage(error.message);
              setToastStatus("error");
              setShowToast(true);
              // notify("error", "Something went wrong, please try again later");
            }
          }, 5000);
          return;
        }
        setServerDetails(() => ({
          serverUrl: response.data.livekit_server_URL,
          token: response.data.access_token,
        }));

        setPreJoinShow(() => false);
        setCanJoin(() => true);
      } catch (error) {
        setToastMessage(error?.message);
        setToastStatus("error");
        setShowToast(true);
      } finally {
        setIsLoading(false);
      }
    },
    [
      getRegion,
      id,
      username,
      isPasswordProtected,
      isValid,
      email,
      password,
      // notify,
      setPreJoinShow,
      setServerDetails,
    ]
  );

  const handleJoinMeeting = useCallback(
    (user) => {
      getServerDetails(user);
    },
    [getServerDetails]
  );

  const handleValidation = useCallback(() => {
    const trimmedUsername = username.trim();
    const trimmedPassword = password.trim();
    const trimmedEmail = email.trim();

    if (
      trimmedUsername === "" ||
      trimmedPassword === "" ||
      trimmedEmail === ""
    ) {
      setIsValid(false);
      return false;
    }

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/;

    if (!emailPattern.test(trimmedEmail)) {
      setIsValid(false);
      return false;
    }

    setIsValid(true);
    return true;
  }, [username, password, email]);

  const handleCommonPasswordValidation = useCallback(() => {
    const trimmedUsername = username.trim();
    const trimmedPassword = password.trim();

    if (trimmedUsername === "" || trimmedPassword === "") {
      setIsValid(false);
      return false;
    }

    setIsValid(true);
    return true;
  }, [username, password]);

  const validateEmail = (email) => {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/;
    if (!email) {
      setEmailError("");
      return false;
    }

    // Only validate if email is at least 3 characters long
    if (email.length < 3) {
      setEmailError("");
      return false;
    }

    if (!emailPattern.test(email)) {
      setEmailError("Please enter a valid email address");
      return false;
    }
    setEmailError("");
    return true;
  };

  useEffect(() => {
    if (
      isPasswordProtected &&
      !isHost &&
      cohostDetail?.current_session_uid !== meetingDetails?.current_session_uid
    )
      handleValidation();

    if (
      meetingDetails?.is_common_password &&
      !isHost &&
      cohostDetail?.current_session_uid !== meetingDetails?.current_session_uid
    ) {
      handleCommonPasswordValidation();
    }

    if (
      (meetingDetails?.is_common_password || isPasswordProtected) &&
      (isHost ||
        cohostDetail?.current_session_uid ===
          meetingDetails?.current_session_uid)
    ) {
      setIsValid(true);
    }
  }, [
    username,
    password,
    email,
    handleValidation,
    isPasswordProtected,
    isHost,
    cohostDetail,
  ]);

  useEffect(() => {
    if (queryParams && queryParams.edit) {
      if (!queryParams.name && username) {
        userChoices.username = username;
      } else {
        userChoices.username = queryParams.name;
        setUsername(queryParams.name);
      }
    } else if (queryParams && queryParams.name) {
      userChoices.username = queryParams.name;
      setUsername(queryParams.name);
    } else {
      userChoices.username = username;
    }
  }, [queryParams]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (showToast) setShowToast(false);
    }, 5000);

    // Clear the timeout if the component unmounts or if showToast changes
    return () => clearTimeout(timer);
  }, [showToast]);

  return (
    <div className="min-vh-100 d-flex align-items-center">
      {isElectron() && (
        <TitleBar
          title={meetingDetails?.event_name}
          pipEnabled={false}
          setIsPipWindow={setIsPipWindow}
          isPipWindow={isPipWindow}
        />
      )}

      <div className="container px-sm-2  px-md-5 px-lg-5">
        {/* Single Row with Two Columns */}
        <div
          className="row justify-content-center align-items-center video-height-control-row"
          style={{ minHeight: "400px" }}
        >
          <div className="col-12 col-md-7 mb-0 mb-md-0 d-flex justify-content-center video-column">
            <div
              className="d-flex flex-column align-items-center justify-content-center text-center w-100"
              style={{ height: "100%" }}
            >
              <div
                className="d-flex justify-content-center w-100"
                style={{ height: "100%" }}
              >
                <PreJoinAudioVideo
                  defaults={userChoices}
                  username={username}
                  setIsValid={setIsValid}
                  setUserChoices={setUserChoices}
                  onSubmit={handleJoinMeeting}
                  setToastMessage={setToastMessage}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                  data-lk-theme="default"
                  onValidate={
                    isPasswordProtected &&
                    !isHost &&
                    cohostDetail?.current_session_uid !==
                      meetingDetails?.current_session_uid
                      ? handleValidation
                      : null
                  }
                  room={room}
                  backgrounds={backgrounds}
                  setBackgrounds={setBackgrounds}
                  isSelfVideoMirrored={isSelfVideoMirrored}
                  setIsSelfVideoMirrored={setIsSelfVideoMirrored}
                  deviceIdAudio={deviceIdAudio}
                  setDeviceIdAudio={setDeviceIdAudio}
                  deviceIdVideo={deviceIdVideo}
                  setDeviceIdVideo={setDeviceIdVideo}
                  brightness={brightness}
                  onBrightnessChange={onBrightnessChange}
                  outputVolume={outputVolume}
                  onOutputVolumeChange={onOutputVolumeChange}
                  autoVideoOff={autoVideoOff}
                  onAutoVideoOffChange={onAutoVideoOffChange}
                  autoAudioOff={autoAudioOff}
                  onAutoAudioOffChange={onAutoAudioOffChange}
                  speakerDeviceId={speakerDeviceId}
                  setSpeakerDeviceId={setSpeakerDeviceId}
                  style={{
                    width: "100%",
                    maxWidth: "100%",
                    height: "100%",
                    transform: "scale(0.9)",
                    transformOrigin: "center center",
                    "@media (max-width: 768px)": {
                      transform: "scale(0.8)",
                    },
                  }}
                />
              </div>
            </div>
          </div>
          <div className="col-md-5 d-flex justify-content-center py-0 px-4 md:px-2 form-column">
            <div
              className="d-flex flex-column align-items-center justify-content-center w-100"
              style={{ height: "100%" }}
            >
              {isLoading ? (
                <Loader
                  heading="Preparing to join...."
                  description="You will be able to join shortly"
                  isLoading
                />
              ) : isRejected &&
                !canJoin &&
                !isHost &&
                cohostDetail?.current_session_uid !==
                  meetingDetails?.current_session_uid ? (
                <div className="not-allowed">
                  <Loader
                    heading="You are not allowed to join the meeting"
                    description="Please contact the host to join the meeting"
                    isLoading={false}
                  />
                </div>
              ) : isLobbyMode &&
                !canJoin &&
                !isHost &&
                cohostDetail?.current_session_uid !==
                  meetingDetails?.current_session_uid &&
                isMeetingStarted ? (
                <Loader
                  heading="Asking the host to join...."
                  description="Please wait for a moment"
                  isLoading
                />
              ) : !isMeetingStarted &&
                !canJoin &&
                !isHost &&
                cohostDetail?.current_session_uid !==
                  meetingDetails?.current_session_uid ? (
                <Loader
                  heading="Waiting to join...."
                  description={`Please wait for the host to start the ${
                    isWebinarMode ? "webinar" : "meeting"
                  }`}
                  isLoading
                />
              ) : (
                <div
                  className="container-parent-form w-100 d-flex flex-column align-items-center justify-content-center"
                  style={{
                    maxHeight: "100%",
                    margin: "0 auto",
                    transition: "all 0.3s ease",
                  }}
                >
                  <div
                    className={`
                      ${isElectron() ? "prejoin-logo-electron" : ""}
                      mb-4 d-flex justify-content-center
                      ${
                        saasMeetingConfigurations() && saasMeetingConfigurations()?.branding_logo_url
                          ? "prejoin-logo-saas"
                          : ""
                      }
                    `}
                  >
                    {saasMeetingConfigurations() && saasMeetingConfigurations()?.branding_logo_url ? (
                      <img src={saasMeetingConfigurations()?.branding_logo_url} alt="Logo" />
                    ) : (
                      <DaakiaLogo />
                    )}
                  </div>
                  <p className="text-center prejoin-page-heading mb-3 responsive-heading">
                    Ready to join the call
                  </p>
                  <p className="text-center prejoin-page-description responsive-description">
                    Setup your audio and video before joining
                  </p>
                  <form
                    className={`prejoin-form w-100 d-flex flex-column align-items-center ${
                      showSideBySideLayout ? "name-only" : ""
                    }`}
                    style={{
                      transition: "all 0.3s ease",
                      transformOrigin: "center center",
                    }}
                  >
                    <div
                      className={`form-input-container w-100 responsive-input ${
                        !showSideBySideLayout ? "mb-2" : ""
                      }`}
                    >
                      <Input
                        id="prejoin-username"
                        size="large"
                        disabled={isHost}
                        placeholder="Your Name *"
                        value={username}
                        onChange={(e) => {
                          setUserChoices({
                            ...userChoices,
                            username: e.target.value,
                          });
                          setUsername(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          !isPasswordProtected &&
                            e.key === "Enter" &&
                            handleJoinMeeting(userChoices);
                        }}
                        className="prejoin-page-input"
                        suffix
                      />

                      {/* Render button inside the name input container when in side-by-side mode */}
                      {showSideBySideLayout && (
                        <Button
                          type="primary"
                          size="large"
                          className={`prejoin-page-join-button ${
                            isValid
                              ? "join-button-enabled"
                              : "join-button-disabled"
                          }`}
                          onClick={() => handleJoinMeeting(userChoices)}
                          disabled={!username.trim()}
                          style={{
                            minWidth: "120px",
                            height: "40px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          Join Meeting
                        </Button>
                      )}
                    </div>

                    {isPasswordProtected &&
                      !isHost &&
                      cohostDetail?.current_session_uid !==
                        meetingDetails?.current_session_uid && (
                        <>
                          <div className="form-input-container w-100 mb-2">
                            <Input
                              id="prejoin-email"
                              size="large"
                              placeholder="Email *"
                              value={email}
                              onChange={(e) => {
                                setEmail(e.target.value);
                                validateEmail(e.target.value);
                              }}
                              className={`prejoin-page-input ${
                                emailError ? "error" : ""
                              }`}
                              status={emailError ? "error" : ""}
                              suffix
                            />
                            {emailError && (
                              <div
                                style={{
                                  color: "red",
                                  fontSize: "12px",
                                  marginTop: "4px",
                                }}
                              >
                                {emailError}
                              </div>
                            )}
                          </div>
                          <div className="form-input-container w-100 mb-2">
                            <Input.Password
                              id="prejoin-password"
                              size="large"
                              placeholder="Password *"
                              value={password}
                              onChange={(e) => setPassword(e.target.value)}
                              iconRender={(visible) =>
                                visible ? (
                                  <EyeTwoTone />
                                ) : (
                                  <EyeInvisibleOutlined />
                                )
                              }
                              className="prejoin-page-input"
                              suffix
                            />
                          </div>
                        </>
                      )}
                    {meetingDetails?.is_common_password &&
                      !isHost &&
                      cohostDetail?.current_session_uid !==
                        meetingDetails?.current_session_uid && (
                        <div className="form-input-container w-100 mb-2">
                          <Input.Password
                            id="prejoin-password"
                            size="large"
                            placeholder="Password *"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            iconRender={(visible) =>
                              visible ? (
                                <EyeTwoTone />
                              ) : (
                                <EyeInvisibleOutlined />
                              )
                            }
                            className="prejoin-page-input"
                            suffix
                          />
                        </div>
                      )}

                    {/* Only render the button outside when not in side-by-side mode */}
                    {!showSideBySideLayout && (
                      <div className="form-input-container w-100 d-flex justify-content-center mt-1">
                        <Button
                          type="primary"
                          size="large"
                          className={`prejoin-page-join-button ${
                            isValid
                              ? "join-button-enabled"
                              : "join-button-disabled"
                          }`}
                          onClick={() => handleJoinMeeting(userChoices)}
                          disabled={
                            (isPasswordProtected &&
                              !isHost &&
                              (!username.trim() ||
                                !email.trim() ||
                                !password.trim())) ||
                            (meetingDetails?.is_common_password &&
                              !isHost &&
                              cohostDetail?.current_session_uid !==
                                meetingDetails?.current_session_uid &&
                              (!username.trim() || !password.trim())) ||
                            (!isPasswordProtected &&
                              !(
                                meetingDetails?.is_common_password &&
                                !isHost &&
                                cohostDetail?.current_session_uid !==
                                  meetingDetails?.current_session_uid
                              ) &&
                              !username.trim())
                          }
                          style={{
                            width: "30%",
                            height: "40px",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                          }}
                        >
                          Join Meeting
                        </Button>
                      </div>
                    )}
                  </form>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {showToast && (
        <StatusNotification status={toastStatus} message={toastMessage} />
      )}
    </div>
  );
}
