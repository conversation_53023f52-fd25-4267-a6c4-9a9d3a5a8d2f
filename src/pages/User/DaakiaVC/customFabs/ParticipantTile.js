/* eslint no-use-before-define: 0 */
import * as React from "react";
import { useEffect } from "react";
import { Track } from "livekit-client";
import { Avatar } from "antd";
import "../styles/ParticipantTile.scss";
import {
  isTrackReference,
  isTrackReferencePinned,
} from "@livekit/components-core";
import {
  ParticipantContext,
  TrackRefContext,
  useEnsureTrackRef,
  useFeatureContext,
  useMaybeLayoutContext,
  useMaybeParticipantContext,
  useMaybeTrackRefContext,
  ParticipantName,
  TrackMutedIndicator,
  ConnectionQualityIndicator,
  FocusToggle,
  // ParticipantPlaceholder,
  LockLockedIcon,
  ScreenShareIcon,
  VideoTrack,
  AudioTrack,
  useParticipantTile,
  useIsEncrypted,
} from "@livekit/components-react";
import { BackgroundBlur, VirtualBackground } from "@livekit/track-processors";
import { generateAvatar } from "../utils/helper";
import { useParticipantContext } from "../context/ParticipantContext";
import { darkColors } from "../utils/constants";
import { RaiseHandOverlay } from "../components/raisehand/RaiseHandOverlay";
import { ReactionsOverlay } from "../components/reactions/ReactionOverlay";
import { useVirtualBackground } from "../context/indexContext";


export function ParticipantContextIfNeeded({ participant, children }) {
  const hasContext = !!useMaybeParticipantContext();
  return participant && !hasContext ? (
    <ParticipantContext.Provider value={participant}>
      {children}
    </ParticipantContext.Provider>
  ) : (
    <>{children}</>
  );
}

function TrackRefContextIfNeeded({ trackRef, children }) {
  const hasContext = !!useMaybeTrackRefContext();
  return trackRef && !hasContext ? (
    <TrackRefContext.Provider value={trackRef}>
      {children}
    </TrackRefContext.Provider>
  ) : (
    <>{children}</>
  );
}

export function ParticipantTile(
  {
    trackRef,
    showEmojiReaction,
    setShowEmojiReaction,
    showRaiseHand,
    remoteRaisedHands = new Map(),
    remoteEmojiReactions = new Map(),
    setRemoteEmojiReactions,
    children,
    onParticipantClick,
    disableSpeakingIndicator,
    isSelfVideoMirrored,
    setIsSelfVideoMirrored,
    brightness = 100,
    participantBrightness = new Map(),
    focusTrack=false,
    ...htmlProps
  },
  ref
) {
  const trackReference = useEnsureTrackRef(trackRef);
  const { elementProps } = useParticipantTile({
    htmlProps,
    disableSpeakingIndicator,
    onParticipantClick,
    trackRef: trackReference,
  });
  const isEncrypted = useIsEncrypted(trackReference.participant);
  const layoutContext = useMaybeLayoutContext();
  const autoManageSubscription = useFeatureContext()?.autoSubscription;
  const { participantColors, setParticipantColors } = useParticipantContext();
  const { currentEffect } = useVirtualBackground();
  // This is used to clear the pin when the participant is unsubscribed
  const handleSubscribe = React.useCallback(
    (subscribed) => {
      if (
        trackReference.source &&
        !subscribed &&
        layoutContext &&
        layoutContext.pin.dispatch &&
        isTrackReferencePinned(trackReference, layoutContext.pin.state)
      ) {
        layoutContext.pin.dispatch({ msg: "clear_pin" });
      }
    },
    [trackReference, layoutContext]
  );

  useEffect(() => {
    const applyEffectToTrack = async () => {
      if (!trackRef || !currentEffect || !trackRef.participant.isLocal) return;
      
      const track = trackRef.publication?.track;
      if (!track || track.source !== Track.Source.Camera) return;
      
      try {
        if (currentEffect.type === 'blur') {
          await track.setProcessor(BackgroundBlur(currentEffect.value, { delegate: "GPU" }));
        } else if (currentEffect.type === 'background') {
          await track.setProcessor(VirtualBackground(currentEffect.value));
        }
      } catch (error) {
        console.error("Error applying effect in ParticipantTile:", error);
      }
    };
    
    applyEffectToTrack();
  }, [trackRef, currentEffect]);


  const [avatarName, setAvatarName] = React.useState("");
  React.useEffect(() => {
    if (trackReference.participant) {
      setAvatarName(
        trackReference.participant.name
          ? generateAvatar(trackReference.participant.name)
          : generateAvatar(trackReference.participant.identity)
      );
    } else {
      setAvatarName("YO");
    }
  }, [trackReference.participant.name]);

  const [randomColor, setRandomColor] = React.useState("#fd4563");

  React.useEffect(() => {
    // Check if we already have a color for this participant
    if (trackReference.participant && participantColors) {
      const participantId = trackReference.participant.identity;
      const existingColor = participantColors.get(participantId);

      if (existingColor) {
        // Use the existing color
        setRandomColor(existingColor);
      } else {
        // Generate a new color and save it
        const randomNumber = Math.floor(Math.random() * 50);
        const newColor = darkColors[randomNumber];
        setRandomColor(newColor);

        // Save the color for this participant
        if (setParticipantColors) {
          setParticipantColors(prevColors => {
            const newColors = new Map(prevColors);
            newColors.set(participantId, newColor);
            return newColors;
          });
        }
      }
    }
  }, [trackReference.participant, participantColors, setParticipantColors]);

  React.useEffect(() => {
    if (!trackReference || !trackReference.participant) return;
    const reaction = remoteEmojiReactions.get(
      trackReference.participant.identity
    );
    if (!reaction) return;
    const timeout = setTimeout(() => {
      setRemoteEmojiReactions((prev) => {
        const newMap = new Map(prev);
        newMap.delete(trackReference.participant.identity);
        return newMap;
      });
    }, 6000);
    return () => clearTimeout(timeout);
  }, [trackReference.participant, remoteEmojiReactions]);

  React.useEffect(() => {
    if (!trackReference.participant || !trackReference.participant.isLocal)
      return;
    if (!showEmojiReaction) return;
    trackReference.participant.setIsSpeaking(true);
    const timeout = setTimeout(() => {
      setShowEmojiReaction(null);
      trackReference.participant.setIsSpeaking(false);
    }, 6000);
    return () => clearTimeout(timeout);
  }, [trackReference.participant, showEmojiReaction]);

  React.useEffect(() => {
    if (!trackReference.participant || !trackReference.participant.isLocal)
      return;
    if (!showEmojiReaction && !showRaiseHand) return;
    trackReference.participant.setIsSpeaking(true);
    const timeout = setTimeout(() => {
      trackReference.participant.setIsSpeaking(false);
    }, 500);
    return () => clearTimeout(timeout);
  }, [showEmojiReaction, showRaiseHand]);

  return (
    <div ref={ref} style={{ position: "relative" }} {...elementProps}>
      <TrackRefContextIfNeeded trackRef={trackReference}>
        <ParticipantContextIfNeeded participant={trackReference.participant}>
          {children ?? (
            <>
              {isTrackReference(trackReference) &&
              (trackReference.publication?.kind === "video" ||
                trackReference.source === Track.Source.Camera ||
                trackReference.source === Track.Source.ScreenShare) ? (
                <VideoTrack
                  trackRef={trackReference}
                  onSubscriptionStatusChanged={handleSubscribe}
                  manageSubscription={autoManageSubscription}
                  style={{
                    transform:
                      trackReference.source === Track.Source.Camera &&
                      trackReference.publication?.track?.mediaStreamTrack?.getSettings()
                        .facingMode !== "environment"
                        ? "rotateY(0deg)"
                        : "",
                    filter: (() => {
                      const filters = [];

                      // Add brightness filter for camera tracks
                      if (trackReference.source === Track.Source.Camera) {
                        // Use remote participant's brightness if available, otherwise use local brightness
                        const participantId = trackReference.participant.identity;
                        const effectiveBrightness = trackReference.participant.isLocal
                          ? brightness
                          : (participantBrightness.get(participantId) || 100);
                        filters.push(`brightness(${effectiveBrightness}%)`);
                      }

                      // Add blur filter for screen share
                      if (trackReference.source === Track.Source.ScreenShare &&
                          trackReference.participant.isLocal) {
                        filters.push("blur(70px)");
                      }

                      return filters.join(" ");
                    })(),
                  }}
                  className={trackReference.source === Track.Source.Camera &&
                    trackReference.participant.isLocal
                    ? isSelfVideoMirrored
                      ? "mirrored-video"
                      : "not-mirrored-video"
                    : ""
                  }
                />
              ) : (
                isTrackReference(trackReference) && (
                  <AudioTrack
                    trackRef={trackReference}
                    onSubscriptionStatusChanged={handleSubscribe}
                  />
                )
              )}
              {trackReference.source === Track.Source.ScreenShare &&
                trackReference.participant.isLocal && (
                  <div className="lk-screen-share-overlay">
                    <span>
                      You are sharing your screen.
                    </span>
                  </div>
                )}
              {showEmojiReaction && trackReference.participant.isLocal && (
                <ReactionsOverlay reaction={showEmojiReaction} />
              )}
              {showRaiseHand && trackReference.participant.isLocal && (
                <RaiseHandOverlay />
              )}
              {remoteRaisedHands.get(trackReference.participant.identity) && (
                <RaiseHandOverlay />
              )}
              {remoteEmojiReactions.get(
                trackReference.participant.identity
              ) && (
                <ReactionsOverlay
                  reaction={remoteEmojiReactions.get(
                    trackReference.participant.identity
                  )}
                />
              )}
              <div className="lk-participant-placeholder">
                {/* <ParticipantPlaceholder /> */}
                <Avatar
                  className={`avatar-style primary-font ${focusTrack ? "focus-track-avatar" : ""}`}
                  style={{ backgroundColor: randomColor }}
                >
                  {avatarName}
                </Avatar>
              </div>
              <div className={`lk-participant-metadata ${focusTrack ? "focus-track-metadata" : ""}`}>
                <div className={`lk-participant-metadata-item`}>
                  {trackReference.source === Track.Source.Camera ? (
                    <>
                      {isEncrypted && (
                        <LockLockedIcon style={{ marginRight: "0.25rem" }} />
                      )}
                      <TrackMutedIndicator
                        trackRef={{
                          participant: trackReference.participant,
                          source: Track.Source.Microphone,
                        }}
                        show={"muted"}
                      />
                      <ParticipantName />
                    </>
                  ) : (
                    <>
                      <ScreenShareIcon style={{ marginRight: "0.25rem" }} />
                      <ParticipantName>&apos;s screen</ParticipantName>
                    </>
                  )}
                </div>
                <ConnectionQualityIndicator className="lk-participant-metadata-item" />
              </div>
            </>
          )}
          <FocusToggle trackRef={trackReference} />
        </ParticipantContextIfNeeded>
      </TrackRefContextIfNeeded>
    </div>
  );
}

ParticipantTile = React.forwardRef(ParticipantTile); // eslint-disable-line
