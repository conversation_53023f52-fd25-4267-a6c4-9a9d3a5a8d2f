import React, { createContext, useContext, useState, useMemo } from 'react'

const VirtualBackgroundContext = createContext()
export function VirtualBackgroundProvider({ children }) {
  const [currentEffect, setCurrentEffect] = useState(null)

  const contextValue = useMemo(() => ({
    currentEffect,
    setCurrentEffect,
  }), [currentEffect])

  return (
    <VirtualBackgroundContext.Provider value={contextValue}>
      {children}
    </VirtualBackgroundContext.Provider>
  )
}


export function IndexProvider({ children }) {
  return (
    <VirtualBackgroundProvider>
      {children}
    </VirtualBackgroundProvider>
  )
}

export function useVirtualBackground() {
  const context = useContext(VirtualBackgroundContext)
  if (!context) throw new Error('useVirtualBackground must be used within VirtualBackgroundProvider')
  return context
}

export default VirtualBackgroundContext