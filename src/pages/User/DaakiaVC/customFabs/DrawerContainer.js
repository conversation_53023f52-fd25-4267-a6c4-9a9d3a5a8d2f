import React from "react";
import { isMobileBrowser } from "@livekit/components-core";
import { DrawerState } from "../utils/constants";

// Component imports
import ChatsDrawer from "../components/chats/ChatsDrawer";
import LiveCaptionsDrawer from "../components/LiveCaptions/LiveCaptionsDrawer";
import { ParticipantList } from "../components/participants/ParticipantList";
import HostControlDrawer from "../components/settings/HostControlDrawer";
import BreakoutRoomDrawer from "../components/participants/BreakoutRoomDrawer";
import { VirtualBackgroundDrawer } from "../components/settings/VirtualBackgroundDrawer";
import { ReportProblemDrawer } from "../components/settings/ReportProblemDrawer";
import { RecordingConsentDrawer } from "../SaaS/features/RecrodingConsent/RecordingConsentDrawer";
import { useVirtualBackground } from "../context/indexContext";

export default function DrawerContainer({
  // layoutType,
  // Context and drawer state
  openDrawer,
  meetingFeatures,
  
  // Room and participants
  room,
  
  // Props and settings
  id,
  isHost,
  isCoHost,
  isWhiteboardOpen,
  meetingDetails,
  coHostToken,
  
  // Toast notifications
  setToastNotification,
  setToastStatus,
  setShowToast,
  
  // Chat drawer props
  formatChatMessageLinks,
  privatechatparticipants,
  setPrivateChatParticipants,
  selectedPrivateChatParticipant,
  setSelectedPrivateChatParticipant,
  showPrivateChat,
  setShowPrivateChat,
  privatechatmessages,
  setPrivateChatMessages,
  newMessageRender,
  setPrivateChatUnreadMessagesCount,
  privateChatUnreadMessagesCount,
  setPublicChatUnreadMessagesCount,
  publicChatUnreadMessagesCount,
  publicChatMessages,
  setPublicChatMessages,
  
  // Live captions props
  liveCaptionData,
  liveCaptionsObject,
  setLiveCaptionsObject,
  setFinalCaptions,
  finalCaptions,
  translationDetails,
  setTranslationDetails,
  finalTranslatedCaptions,
  setFinalTranslatedCaptions,
  
  // Participant list props
  layoutContext,
  lobbyParticipants,
  setRemoteRaisedHands,
  setShowRaiseHand,
  currentRoomName,
  setBreakoutRoomDuration,
  breakoutRooms,
  setBreakoutRooms,
  setRoomKeyCounter,
  isBreakoutRoomCnfigSet,
  setIsBreakoutRoomCnfigSet,
  forceMute,
  forceVideoOff,
  isBreakoutRoom,
  allowLiveCollabWhiteBoard,
  removeParticipantFromLobby,
  clearAllLobbyParticipants,
  
  // Host control props
  setForceVideoOff,
  setForceMute,
  setCanDownloadChatAttachment,
  canDownloadChatAttachment,
  setAllowLiveCollabWhiteBoard,
  
  // Breakout room props
  roomKeyCounter,
  breakoutRoomDuration,
  
  // Report problem props
  clientPreferedServerId,
  
  // Recording consent props
  participantConsent,
}) {
  const {setCurrentEffect } = useVirtualBackground();

  const commonDrawerProps = {
    setToastNotification,
    setToastStatus,
    setShowToast,
    meetingDetails,
    coHostToken,
    room,
    id,
    isHost,
    isCoHost,
    isWhiteboardOpen,
    meetingFeatures,
  };

  const chatDrawerProps = {
    messageFormatter: formatChatMessageLinks,
    privatechatparticipants,
    setprivatechatparticipants: setPrivateChatParticipants,
    selectedprivatechatparticipant: selectedPrivateChatParticipant,
    setselectedprivatechatparticipant: setSelectedPrivateChatParticipant,
    showPrivateChat,
    setShowPrivateChat,
    localparticipant: room.localParticipant,
    privatechatmessages,
    setprivatechatmessages: setPrivateChatMessages,
    newmessagerender: newMessageRender,
    setPrivateChatUnreadMessagesCount,
    privateChatUnreadMessagesCount,
    setPublicChatUnreadMessagesCount,
    publicChatUnreadMessagesCount,
    canDownloadChatAttachment,
    publicchatmessages: publicChatMessages,
    setpublicchatmessages: setPublicChatMessages,
    ...commonDrawerProps,
  };

  const participantListProps = {
    remoteParticipants: room.remoteParticipants,
    localParticipant: room.localParticipant,
    showParticipantsList: openDrawer === DrawerState.PARTICIPANTS,
    layoutContext,
    lobbyParticipants,
    setRemoteRaisedHands,
    setShowRaiseHand,
    currentRoomName,
    setBreakoutRoomDuration,
    breakoutRooms,
    setBreakoutRooms,
    setRoomKeyCounter,
    isBreakoutRoomCnfigSet,
    setIsBreakoutRoomCnfigSet,
    setselectedprivatechatparticipant: setSelectedPrivateChatParticipant,
    setprivatechatparticipants: setPrivateChatParticipants,
    privatechatparticipants,
    setshowprivatechat: setShowPrivateChat,
    forcemute: forceMute,
    forcevideooff: forceVideoOff,
    isBreakoutRoom,
    allowLiveCollabWhiteBoard,
    removeParticipantFromLobby,
    clearAllLobbyParticipants,
    ...commonDrawerProps,
  };

  return (
    <>
      {/* Chat Drawer */}
      {meetingFeatures?.conference_chat === 1 &&
        openDrawer === DrawerState.CHAT &&
        (isMobileBrowser() ? (
          <ChatsDrawer {...chatDrawerProps} />
        ) : (
          <ChatsDrawer {...chatDrawerProps} />
        ))}

      {/* Live Captions */}
      {meetingFeatures?.voice_transcription === 1 &&
        openDrawer === DrawerState.LIVECAPTION &&
        !isMobileBrowser() && (
          <LiveCaptionsDrawer
            isLiveCaptionsDrawerOpen={openDrawer === DrawerState.LIVECAPTION}
            remoteParticipants={room.remoteParticipants}
            localParticipant={room.localParticipant}
            liveCaptionData={liveCaptionData}
            livecaptionsobject={liveCaptionsObject}
            setlivecaptionsobject={setLiveCaptionsObject}
            setfinalcaptions={setFinalCaptions}
            finalcaptions={finalCaptions}
            translationDetails={translationDetails}
            setTranslationDetails={setTranslationDetails}
            finalTranslatedCaptions={finalTranslatedCaptions}
            setFinalTranslatedCaptions={setFinalTranslatedCaptions}
            {...commonDrawerProps}
          />
        )}

      {/* Participant Drawer */}
      {openDrawer === DrawerState.PARTICIPANTS &&
        (isMobileBrowser() ? (
          <div className="side-drawer-mobile">
            <ParticipantList {...participantListProps} />
          </div>
        ) : (
          <ParticipantList {...participantListProps} />
        ))}

      {/* Host Controls */}
      {openDrawer === DrawerState.HOSTCONTROL && (
        <HostControlDrawer
          showHostControl={openDrawer === DrawerState.HOSTCONTROL}
          setForceVideoOff={setForceVideoOff}
          forceVideoOff={forceVideoOff}
          setForceMute={setForceMute}
          forceMute={forceMute}
          setCanDownloadChatAttachment={setCanDownloadChatAttachment}
          canDownloadChatAttachment={canDownloadChatAttachment}
          setAllowLiveCollabWhiteBoard={setAllowLiveCollabWhiteBoard}
          allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
          {...commonDrawerProps}
        />
      )}

      {/* Breakout Rooms */}
      {meetingFeatures?.breakout_room === 1 && openDrawer === DrawerState.BREAKOUTROOM && (
        <BreakoutRoomDrawer
          showBreakoutRoom={openDrawer === DrawerState.BREAKOUTROOM}
          remoteParticipants={room.remoteParticipants}
          localParticipant={room.localParticipant}
          breakoutRooms={breakoutRooms}
          setBreakoutRooms={setBreakoutRooms}
          setRoomKeyCounter={setRoomKeyCounter}
          roomKeyCounter={roomKeyCounter}
          breakoutRoomDuration={breakoutRoomDuration}
          setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
          {...commonDrawerProps}
        />
      )}

      {/* Virtual Background Drawer */}
      {openDrawer === DrawerState.VIRTUAL_BACKGROUND && (
        <VirtualBackgroundDrawer
          isVBDrawerOpen={openDrawer === DrawerState.VIRTUAL_BACKGROUND}
          setCurrentEffect={setCurrentEffect}
          {...commonDrawerProps}
        />
      )}

      {/* Report Problem */}
      {openDrawer === DrawerState.REPORT_ISSUE && (
        <ReportProblemDrawer
          isRPDrawerOpen={openDrawer === DrawerState.REPORT_ISSUE}
          clientPreferedServerId={clientPreferedServerId}
          localParticipant={room?.localParticipant}
          {...commonDrawerProps}
        />
      )}

      {/* Recording Consent Drawer */}
      {openDrawer === DrawerState.RECORDING_CONSENT && (
        <RecordingConsentDrawer
          showRecordingConsentDrawer={openDrawer === DrawerState.RECORDING_CONSENT}
          participantConsent={participantConsent}
          room={room}
        />
      )}
    </>
  );
};
